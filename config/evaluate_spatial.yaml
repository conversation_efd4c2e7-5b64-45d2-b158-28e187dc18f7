# Variables section - define reusable values
model_name: "gpt-5-mini"
task_name: "PassiveDir"
num: 20

defaults:
  - base
  - envs_spatial
  - evaluate_api_llm

output_dir: "results/${model_name}/${task_name}"

eval_model_type: api # api or vllm
# api model
api_model_info:
  model_name: ${model_name}
  max_concurrency: 128

# vllm model
model_path: Qwen/Qwen2.5-3B-Instruct
actor_rollout_ref:
  rollout:
    max_model_len: 10000
    gpu_memory_utilization: 0.8
    val_kwargs: # for vllm
      temperature: 0
      top_p: 1

agent_proxy:
  max_turn: 20 # should be same as max_actions_per_traj
  action_sep: "||"
  max_actions_per_turn: 1 # how many actions can be output at most in a single turn
  enable_think: true

es_manager:
  val:
    env_groups: ${num} # 450
    group_size: 1 # should be set to 1 because when val temperature is set to 0 and group size > 1, there will be repetitive prompts which leads to same trajectory.
    env_configs:
      # tags: ["PassiveRot", "PassiveRotDual", "PassiveCircularRot", "PassivePoV", "PassiveDir", "PassiveE2A", "PassiveLoc", "PassiveFalseBeliefRotation", "PassiveFalseBeliefMovement"]
      # n_groups: [25, 25, 25, 25, 25, 25, 25, 25, 25]
      tags:
        - "${task_name}"
      n_groups:
        - ${num}
      # tags: ["ActiveDir"]
      # n_groups: [100]


